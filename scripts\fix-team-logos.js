/**
 * Script para corrigir logos dos times que estão sem logo
 */

import { initializeDatabase, executeQuery } from '../lib/database-config.js'

// Mapeamento de times brasileiros com suas logos corretas
const TEAM_LOGO_FIXES = {
  'Athletico Paranaense': 'https://crests.football-data.org/1773.png',
  'Athletico-PR': 'https://crests.football-data.org/1773.png',
  'Atlético Clube Goianiense': 'https://crests.football-data.org/1766.png',
  'Atlético-GO': 'https://crests.football-data.org/1766.png',
  'Atlético-MG': 'https://crests.football-data.org/1766.png',
  'Bahia Esporte Clube': 'https://crests.football-data.org/1777.png',
  'Bahia': 'https://crests.football-data.org/1777.png',
  'Botafogo de Futebol e Regatas': 'https://crests.football-data.org/1770.png',
  'Botafogo': 'https://crests.football-data.org/1770.png',
  'Ceará SC': 'https://crests.football-data.org/1837.png',
  'Cear<PERSON>': 'https://crests.football-data.org/1837.png',
  'SC Corinthians Paulista': 'https://crests.football-data.org/1779.png',
  'Corinthians': 'https://crests.football-data.org/1779.png',
  'Cruzeiro EC': 'https://crests.football-data.org/1771.png',
  'Cruzeiro': 'https://crests.football-data.org/1771.png',
  'CR Flamengo': 'https://crests.football-data.org/1783.png',
  'Flamengo': 'https://crests.football-data.org/1783.png',
  'Fluminense FC': 'https://crests.football-data.org/1765.png',
  'Fluminense': 'https://crests.football-data.org/1765.png',
  'Fortaleza EC': 'https://crests.football-data.org/3984.png',
  'Fortaleza': 'https://crests.football-data.org/3984.png',
  'Grêmio FBPA': 'https://crests.football-data.org/1767.png',
  'Grêmio': 'https://crests.football-data.org/1767.png',
  'SE Palmeiras': 'https://crests.football-data.org/1769.png',
  'Palmeiras': 'https://crests.football-data.org/1769.png',
  'Santos FC': 'https://crests.football-data.org/6685.png',
  'Santos': 'https://crests.football-data.org/6685.png',
  'São Paulo FC': 'https://crests.football-data.org/1776.png',
  'São Paulo': 'https://crests.football-data.org/1776.png',
  'Sport Club do Recife': 'https://crests.football-data.org/1778.png',
  'Sport': 'https://crests.football-data.org/1778.png',
  'CR Vasco da Gama': '/images/teams/1897.png',
  'Vasco': '/images/teams/1897.png',
  'EC Vitória': 'https://crests.football-data.org/1782.png',
  'Vitória': 'https://crests.football-data.org/1782.png',
  'RB Bragantino': 'https://crests.football-data.org/4286.png',
  'Bragantino': 'https://crests.football-data.org/4286.png',
  'Mirassol FC': 'https://crests.football-data.org/4364.png',
  'Mirassol': 'https://crests.football-data.org/4364.png',
  'Coritiba Foot Ball Club': 'https://crests.football-data.org/1780.png',
  'Coritiba': 'https://crests.football-data.org/1780.png',
  'Goiás': 'https://crests.football-data.org/1781.png',
  'Internacional': 'https://crests.football-data.org/1768.png',
  'Náutico': 'https://crests.football-data.org/1784.png',
  'Vila Nova': 'https://crests.football-data.org/1785.png'
}

async function fixTeamLogos() {
  try {
    console.log('🔧 Iniciando correção de logos dos times...')
    await initializeDatabase()

    // Buscar times sem logo
    const timesWithoutLogo = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url IS NULL OR logo_url = ''
      ORDER BY nome
    `)

    console.log(`📊 Encontrados ${timesWithoutLogo.length} times sem logo`)

    let fixed = 0
    let notFound = 0

    for (const time of timesWithoutLogo) {
      let logoUrl = null

      // Tentar encontrar logo pelo nome completo
      if (TEAM_LOGO_FIXES[time.nome]) {
        logoUrl = TEAM_LOGO_FIXES[time.nome]
      }
      // Tentar encontrar logo pelo nome curto
      else if (time.nome_curto && TEAM_LOGO_FIXES[time.nome_curto]) {
        logoUrl = TEAM_LOGO_FIXES[time.nome_curto]
      }
      // Tentar busca parcial
      else {
        for (const [teamName, teamLogo] of Object.entries(TEAM_LOGO_FIXES)) {
          if (time.nome.toLowerCase().includes(teamName.toLowerCase()) ||
              (time.nome_curto && time.nome_curto.toLowerCase().includes(teamName.toLowerCase()))) {
            logoUrl = teamLogo
            break
          }
        }
      }

      if (logoUrl) {
        try {
          await executeQuery(
            'UPDATE times SET logo_url = ? WHERE id = ?',
            [logoUrl, time.id]
          )
          console.log(`✅ ${time.nome} (ID: ${time.id}) -> ${logoUrl}`)
          fixed++
        } catch (error) {
          console.error(`❌ Erro ao atualizar ${time.nome}:`, error.message)
        }
      } else {
        console.log(`⚠️ Logo não encontrado para: ${time.nome} (${time.nome_curto})`)
        notFound++
      }
    }

    console.log('\n📊 Resultado da correção:')
    console.log(`✅ Logos corrigidos: ${fixed}`)
    console.log(`⚠️ Times sem logo encontrado: ${notFound}`)
    console.log(`📈 Total processado: ${timesWithoutLogo.length}`)

    // Verificar resultado final
    const finalStats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 END) as com_logo,
        COUNT(CASE WHEN logo_url IS NULL OR logo_url = '' THEN 1 END) as sem_logo
      FROM times
    `)

    const stats = finalStats[0]
    console.log('\n📈 Estatísticas finais:')
    console.log(`📊 Total de times: ${stats.total}`)
    console.log(`✅ Com logo: ${stats.com_logo} (${((stats.com_logo/stats.total)*100).toFixed(1)}%)`)
    console.log(`❌ Sem logo: ${stats.sem_logo} (${((stats.sem_logo/stats.total)*100).toFixed(1)}%)`)

  } catch (error) {
    console.error('❌ Erro na correção de logos:', error)
  }
}

// Executar o script
fixTeamLogos()
